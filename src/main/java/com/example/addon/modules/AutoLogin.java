package meteordevelopment.meteorclient.systems.modules.ggboy;

import fi.dy.masa.malilib.util.StringUtils;
import io.netty.handler.codec.socks.SocksAuthResponseDecoder$1$ConstantPool;
import meteordevelopment.meteorclient.events.packets.PacketEvent;
import meteordevelopment.meteorclient.mixininterface.IBox$ConstantPool;
import meteordevelopment.meteorclient.settings.EnchantmentListSetting$ConstantPool;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.settings.StringSetting;
import meteordevelopment.meteorclient.systems.modules.Categories;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.misc.DiscordPresence$SelectMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.FastClimb$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.WallHack$ConstantPool;
import meteordevelopment.meteorclient.utils.usercheck.CheckUtils;
import meteordevelopment.meteorclient.utils.world.BlockIterator$Callback$ConstantPool;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.network.packet.s2c.play.GameMessageS2CPacket;
import net.minecraft.text.Text;

public class AutoLogin extends Module {
   private final SettingGroup sgGeneral = this.settings.getDefaultGroup();
   private final Setting<String> userPassword = this.sgGeneral
      .add(
         new StringSetting.Builder()
            .name(paeT4Un29y(7lLdTvF6j6(DgtoMqg3jy(IBox$ConstantPool.const_LKvyTvZUiT4ONV6))))
            .description(paeT4Un29y(7lLdTvF6j6(DgtoMqg3jy(DiscordPresence$SelectMode$ConstantPool.const_fYxeDSau5Jr7yNG))))
            .build()
      );

   public AutoLogin() {
      super(
         Categories.Ggboy,
         paeT4Un29y(7lLdTvF6j6(DgtoMqg3jy(EnchantmentListSetting$ConstantPool.const_zrsOyTbA6W7Yr5M))),
         new StringBuilder(paeT4Un29y(7lLdTvF6j6(DgtoMqg3jy(SocksAuthResponseDecoder$1$ConstantPool.const_wgMrLAtyjqfwveG)))),
         paeT4Un29y(7lLdTvF6j6(DgtoMqg3jy(FastClimb$ConstantPool.const_oUCQCveROMgYLj6)))
      );
   }

   @Override
   public void onActivate() {

   }


   @EventHandler
   public void onPacketReceiver(PacketEvent.Receive event) {
      if (event.packet instanceof GameMessageS2CPacket packet
         && packet.content().getString().contains(paeT4Un29y(7lLdTvF6j6(DgtoMqg3jy(BlockIterator$Callback$ConstantPool.const_Zv8N7YfL2P6BU2l))))
         && StringUtils.getStringWidth(this.userPassword.get()) > 0) {
         mc.getNetworkHandler().sendChatCommand("login " + this.userPassword.get());
      }
   }
}
