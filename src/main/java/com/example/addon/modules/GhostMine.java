package com.example.addon.modules;

import io.netty.handler.codec.socks.SocksCommonUtils$ConstantPool;
import io.netty.handler.codec.socks.SocksInitRequestDecoder$1$ConstantPool;
import io.netty.handler.proxy.ProxyHandler$LazyChannelPromise$ConstantPool;
import javassist.CtClassType$ConstantPool;
import javassist.CtMethod$LongConstParameter$ConstantPool;
import javassist.SerialVersionUID$3$ConstantPool;
import javassist.bytecode.CodeIterator$ConstantPool;
import javassist.bytecode.NestHostAttribute$ConstantPool;
import javassist.bytecode.StackMap$Copier$ConstantPool;
import javassist.compiler.JvstTypeChecker$ConstantPool;
import javassist.compiler.ast.Stmnt$ConstantPool;
import javassist.compiler.ast.Visitor$ConstantPool;
import javassist.expr.ExprEditor$ConstantPool;
import javassist.expr.NewExpr$ConstantPool;
import javassist.util.proxy.SecurityActions$4$ConstantPool;
import meteordevelopment.meteorclient.events.entity.player.StartBreakingBlockEvent;
import meteordevelopment.meteorclient.events.packets.PacketEvent;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.gui.renderer.packer.TexturePacker$Image$ConstantPool;
import meteordevelopment.meteorclient.gui.themes.meteor.widgets.WMeteorTopBar$ConstantPool;
import meteordevelopment.meteorclient.gui.utils.WindowConfig$ConstantPool;
import meteordevelopment.meteorclient.gui.widgets.containers.WWindow$ConstantPool;
import meteordevelopment.meteorclient.renderer.ShapeMode;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.friends.Friends$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.Categories;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.combat.AutoWeb$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.combat.Hitboxes$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.CityESP$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.HighwayBuilder$State$16$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.HighwayBuilder$StraightBlockPosProvider$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.Nuker$Mode$ConstantPool;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.meteorclient.utils.player.PlayerUtils;
import meteordevelopment.meteorclient.utils.render.color.Color$ConstantPool;
import meteordevelopment.meteorclient.utils.render.color.SettingColor;
import meteordevelopment.meteorclient.utils.usercheck.CheckUtils;
import meteordevelopment.meteorclient.utils.world.BlockUtils;
import meteordevelopment.meteorclient.utils.world.Timer;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.block.Block;
import net.minecraft.block.BlockState;
import net.minecraft.block.Blocks;
import net.minecraft.item.*;
import net.minecraft.network.packet.c2s.play.PlayerActionC2SPacket;
import net.minecraft.network.packet.c2s.play.PlayerActionC2SPacket.Action;
import net.minecraft.util.Hand;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import org.reflections.scanners.MethodAnnotationsScanner$ConstantPool;
import org.reflections.scanners.Scanners$10$ConstantPool;
import org.reflections.serializers.XmlSerializer$ConstantPool;
import org.reflections.util.UtilQueryBuilder$ConstantPool;

import javax.annotation.OverridingMethodsMustInvokeSuper$ConstantPool;
import javax.annotation.RegEx$ConstantPool;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class GrimPacketMine extends Module {
    private final SettingGroup sgGeneral = this.settings.getDefaultGroup();
    public Setting<Integer> range = this.sgGeneral
            .add(
                    new IntSetting.Builder()
                            .name("挖掘范围")
                            .description("设置挖掘方块的最大距离")
                            .sliderRange(1, 6)
                            .defaultValue(6)
                            .build()
            );
    public Setting<Double> speed = this.sgGeneral
            .add(
                    new DoubleSetting.Builder()
                            .name("挖掘速度")
                            .description("控制挖掘方块的速度倍率")
                            .sliderRange(0.65, 1.0)
                            .defaultValue(0.85)
                            .build()
            );
    public Setting<Boolean> doubleBreak = this.sgGeneral
            .add(
                    new BoolSetting.Builder()
                            .name("双重破坏")
                            .description("同时挖掘两个方块以提高效率")
                            .defaultValue(true)
                            .build()
            );
    public Setting<Boolean> rebreak = this.sgGeneral
            .add(new BoolSetting.Builder().name("自动重挖").description("自动重新挖掘已破坏的方块").defaultValue(true).build());
    public Setting<Integer> rebreakDelay = this.sgGeneral
            .add(
                    new IntSetting.Builder()
                            .name("重挖延迟")
                            .description("重新挖掘方块前的延迟时间(毫秒)")
                            .sliderRange(0, 10)
                            .defaultValue(0)
                            .visible(this.rebreak::get)
                            .build()
            );
    public Setting<SwapMode> swapModeSetting = this.sgGeneral
            .add(
                    ((EnumSetting.Builder) ((EnumSetting.Builder) new EnumSetting.Builder()
                            .name("切换模式")
                            .description("选择工具切换的方式"))
                            .defaultValue(SwapMode.NORMAL))
                            .build()
            );
    private final SettingGroup sgRender = this.settings.createGroup("渲染设置");
    private final Setting<Boolean> render = this.sgRender
            .add(
                    new BoolSetting.Builder()
                            .name("显示渲染")
                            .description("是否显示正在挖掘方块的可视化效果")
                            .defaultValue(true)
                            .build()
            );
    private final Setting<ShapeMode> shapeMode = this.sgRender
            .add(
                    ((EnumSetting.Builder) ((EnumSetting.Builder) ((EnumSetting.Builder) new EnumSetting.Builder()
                            .name("形状模式")
                            .description("选择渲染形状的显示方式"))
                            .defaultValue(ShapeMode.Both))
                            .build()
            );
    private final Setting<SettingColor> readySideColor = this.sgRender
            .add(
                    new ColorSetting.Builder()
                            .name("完成侧面颜色")
                            .description("方块挖掘完成时侧面的颜色")
                            .defaultValue(new SettingColor(240, 240, 240, 0))
                            .build()
            );
    private final Setting<SettingColor> readyLineColor = this.sgRender
            .add(
                    new ColorSetting.Builder()
                            .name("完成边框颜色")
                            .description("方块挖掘完成时边框的颜色")
                            .defaultValue(new SettingColor(169, 116, 237, 255))
                            .build()
            );
    private final Setting<SettingColor> sideColor = this.sgRender
            .add(
                    new ColorSetting.Builder()
                            .name("侧面颜色")
                            .description("正在挖掘方块侧面的颜色")
                            .defaultValue(new SettingColor(179, 117, 238, 56))
                            .build()
            );
    private final Setting<SettingColor> lineColor = this.sgRender
            .add(
                    new ColorSetting.Builder()
                            .name("边框颜色")
                            .description("正在挖掘方块边框的颜色")
                            .defaultValue(new SettingColor(169, 116, 237, 255))
                            .build()
            );
    private final List<BlockDate> breakBlocks = new ArrayList<>();
    public static BlockDate firstBlockDate = null;
    public static BlockDate secondBlockDate = null;
    private BlockDate rebreakBlockDate = null;
    public static BlockDate tempBlockDate = null;
    private Timer rebreakTimer = new Timer();
    public static final List<Block> godBlocks = Arrays.asList(
            Blocks.COMMAND_BLOCK,
            Blocks.LAVA_CAULDRON,
            Blocks.LAVA,
            Blocks.WATER_CAULDRON,
            Blocks.WATER,
            Blocks.BEDROCK,
            Blocks.BARRIER,
            Blocks.END_PORTAL,
            Blocks.NETHER_PORTAL,
            Blocks.END_PORTAL_FRAME
    );

    public GrimPacketMine() {
        super(
                Categories.Ggboy,
                "幽灵挖掘",
                "使用数据包挖掘方块，绕过反作弊检测",
                "grim-packet-mine"
        );
    }

    @Override
    public void onActivate() {
        firstBlockDate = null;
        secondBlockDate = null;
        this.rebreakBlockDate = null;
        tempBlockDate = null;
        this.rebreakTimer.reset();
    }

    @Override
    public void onDeactivate() {
        firstBlockDate = null;
        secondBlockDate = null;
        this.rebreakBlockDate = null;
        tempBlockDate = null;
        this.rebreakTimer.reset();
    }

    @Override
    public void init() {
        if (this.isActive() && !CheckUtils.check()) {
            this.toggle();
        }
    }

    public static void reFresh() {
        firstBlockDate = null;
        secondBlockDate = null;
        tempBlockDate = null;
        stopMine(mc.player.getBlockPos());
    }

    @EventHandler
    public void onTick(TickEvent.Pre event) {
        this.rangeCheck();
        if (!this.doubleBreak.get()) {
            if (firstBlockDate != null && mc.world.getBlockState(firstBlockDate.pos).getBlock() == Blocks.AIR) {
                firstBlockDate = null;
            }

            if (firstBlockDate != null && !firstBlockDate.isMining) {
                this.mineBlock(firstBlockDate.pos, firstBlockDate.direction);
                firstBlockDate.isMining = true;
            }

            if (firstBlockDate != null
                    && firstBlockDate.isMining
                    && mc.world.getBlockState(firstBlockDate.pos).getBlock() == Blocks.AIR
                    && this.rebreak.get()) {
                firstBlockDate.isBreaked = true;
            }

            if (firstBlockDate != null && firstBlockDate.isMining && !firstBlockDate.done) {
                firstBlockDate.freshProgress();
            }

            if (firstBlockDate != null && !firstBlockDate.isBreaked && firstBlockDate.isMining && firstBlockDate.done) {
                if (this.swapModeSetting.get() == SwapMode.SILENT) {
                    int slot = this.getBestTool(mc.world.getBlockState(firstBlockDate.pos));
                    if (slot != -1) {
                        InvUtils.swap(slot, true);
                    }
                }

                mc.player.networkHandler.sendPacket(new PlayerActionC2SPacket(Action.STOP_DESTROY_BLOCK, firstBlockDate.pos, firstBlockDate.direction));
                if (this.swapModeSetting.get() == SwapMode.SILENT) {
                    InvUtils.swapBack();
                }

                if (firstBlockDate.rebreak) {
                    this.rebreakBlockDate = firstBlockDate;
                } else {
                    this.rebreakBlockDate = null;
                }

                firstBlockDate = null;
            }

            if (this.rebreakBlockDate != null
                    && firstBlockDate == null
                    && mc.world.getBlockState(this.rebreakBlockDate.pos).getBlock() != Blocks.AIR
                    && mc.world.getBlockState(this.rebreakBlockDate.pos).getBlock() != Blocks.WATER
                    && mc.world.getBlockState(this.rebreakBlockDate.pos).getBlock() != Blocks.LAVA
                    && this.rebreak.get()
                    && this.rebreakTimer.passedMs((long) (this.rebreakDelay.get() * 200))) {
                int slot = this.getBestTool(mc.world.getBlockState(this.rebreakBlockDate.pos));
                if (slot != -1 && slot != mc.player.getInventory().selectedSlot && this.swapModeSetting.get() == SwapMode.SILENT) {
                    InvUtils.swap(slot, true);
                }

                if (slot != -1 && slot != mc.player.getInventory().selectedSlot && this.swapModeSetting.get() == SwapMode.NORMAL) {
                    InvUtils.swap(slot, false);
                }

                mc.player.networkHandler.sendPacket(new PlayerActionC2SPacket(Action.STOP_DESTROY_BLOCK, this.rebreakBlockDate.pos, this.rebreakBlockDate.direction));
                this.rebreakTimer.reset();
                if (slot != -1 && this.swapModeSetting.get() == SwapMode.SILENT) {
                    InvUtils.swapBack();
                }
            }
        } else {
            if (firstBlockDate != null && mc.world.getBlockState(firstBlockDate.pos).getBlock() == Blocks.AIR) {
                firstBlockDate = null;
            }

            if (secondBlockDate != null && mc.world.getBlockState(secondBlockDate.pos).getBlock() == Blocks.AIR) {
                secondBlockDate = null;
            }

            if (firstBlockDate != null && !firstBlockDate.isMining && secondBlockDate == null) {
                this.mineBlock(firstBlockDate.pos, firstBlockDate.direction);
                firstBlockDate.isMining = true;
            }

            if (secondBlockDate != null && !secondBlockDate.isMining) {
                if (firstBlockDate != null) {
                    this.mineBlock(firstBlockDate.pos, firstBlockDate.direction);
                }

                this.mineBlock(secondBlockDate.pos, secondBlockDate.direction);
                secondBlockDate.isMining = true;
            }

            if (firstBlockDate != null && firstBlockDate.isMining && !firstBlockDate.done) {
                firstBlockDate.freshProgress();
            }

            if (secondBlockDate != null && secondBlockDate.isMining && !secondBlockDate.done) {
                secondBlockDate.freshProgress();
            }

            if (tempBlockDate != null && tempBlockDate.isMining && !tempBlockDate.done) {
                tempBlockDate.freshProgress();
            }

            if (tempBlockDate != null && tempBlockDate.done) {
                tempBlockDate = null;
            }

            if (firstBlockDate != null && !firstBlockDate.isBreaked && firstBlockDate.isMining && firstBlockDate.done && secondBlockDate == null) {
                if (this.swapModeSetting.get() == SwapMode.SILENT) {
                    int slotx = this.getBestTool(mc.world.getBlockState(firstBlockDate.pos));
                    if (slotx != -1) {
                        InvUtils.swap(slotx, true);
                    }
                }

                mc.player.networkHandler.sendPacket(new PlayerActionC2SPacket(Action.STOP_DESTROY_BLOCK, firstBlockDate.pos, firstBlockDate.direction));
                if (firstBlockDate.rebreak) {
                    this.rebreakBlockDate = new BlockDate(firstBlockDate.pos, firstBlockDate.direction);
                } else {
                    this.rebreakBlockDate = null;
                }

                firstBlockDate = null;
                if (this.swapModeSetting.get() == SwapMode.SILENT) {
                    InvUtils.swapBack();
                }
            }

            if (firstBlockDate != null && !firstBlockDate.isBreaked && firstBlockDate.isMining && firstBlockDate.done && secondBlockDate != null) {
                if (this.swapModeSetting.get() == SwapMode.SILENT) {
                    int slotx = this.getBestTool(mc.world.getBlockState(secondBlockDate.pos));
                    if (slotx != -1) {
                        InvUtils.swap(slotx, true);
                    }
                }

                if (this.swapModeSetting.get() == SwapMode.NORMAL) {
                    int firstSlot = this.getBestTool(mc.world.getBlockState(firstBlockDate.pos));
                    int secondSlot = this.getBestTool(mc.world.getBlockState(secondBlockDate.pos));
                    if (firstSlot == -1 && secondSlot != -1) {
                        InvUtils.swap(secondSlot, false);
                    }

                    if (firstSlot != -1 && secondSlot == -1) {
                        InvUtils.swap(firstSlot, false);
                    }

                    if (firstSlot != -1 && secondSlot != -1 && firstSlot != secondSlot) {
                        if (mc.player.getInventory().getStack(firstSlot).getItem() instanceof PickaxeItem) {
                            InvUtils.swap(firstSlot, false);
                        } else if (mc.player.getInventory().getStack(secondSlot).getItem() instanceof PickaxeItem) {
                            InvUtils.swap(secondSlot, false);
                        }
                    }
                }

                mc.player.networkHandler.sendPacket(new PlayerActionC2SPacket(Action.STOP_DESTROY_BLOCK, firstBlockDate.pos, firstBlockDate.direction));
                mc.player.networkHandler.sendPacket(new PlayerActionC2SPacket(Action.STOP_DESTROY_BLOCK, secondBlockDate.pos, secondBlockDate.direction));
                firstBlockDate = null;
                if (this.swapModeSetting.get() == SwapMode.SILENT) {
                    InvUtils.swapBack();
                }
            }

            if (secondBlockDate != null && secondBlockDate.done) {
                if (secondBlockDate.rebreak) {
                    this.rebreakBlockDate = new BlockDate(secondBlockDate.pos, secondBlockDate.direction);
                } else {
                    this.rebreakBlockDate = null;
                }

                if (firstBlockDate != null && firstBlockDate.done) {
                    mc.player.networkHandler.sendPacket(new PlayerActionC2SPacket(Action.STOP_DESTROY_BLOCK, firstBlockDate.pos, firstBlockDate.direction));
                }

                mc.player.networkHandler.sendPacket(new PlayerActionC2SPacket(Action.STOP_DESTROY_BLOCK, secondBlockDate.pos, secondBlockDate.direction));
                secondBlockDate = null;
                if (firstBlockDate != null && !firstBlockDate.done) {
                    tempBlockDate = firstBlockDate;
                }

                firstBlockDate = null;
            }

            if (this.rebreakBlockDate != null
                    && firstBlockDate == null
                    && secondBlockDate == null
                    && mc.world.getBlockState(this.rebreakBlockDate.pos).getBlock() != Blocks.AIR
                    && mc.world.getBlockState(this.rebreakBlockDate.pos).getBlock() != Blocks.WATER
                    && mc.world.getBlockState(this.rebreakBlockDate.pos).getBlock() != Blocks.LAVA
                    && this.rebreak.get()
                    && this.rebreakTimer.passedMs((long) (this.rebreakDelay.get() * 200))) {
                int slotx = this.getBestTool(mc.world.getBlockState(this.rebreakBlockDate.pos));
                if (slotx != -1 && slotx != mc.player.getInventory().selectedSlot && this.swapModeSetting.get() == SwapMode.SILENT) {
                    InvUtils.swap(slotx, true);
                }

                if (slotx != -1 && slotx != mc.player.getInventory().selectedSlot && this.swapModeSetting.get() == SwapMode.NORMAL) {
                    InvUtils.swap(slotx, false);
                }

                mc.player.networkHandler.sendPacket(new PlayerActionC2SPacket(Action.STOP_DESTROY_BLOCK, this.rebreakBlockDate.pos, this.rebreakBlockDate.direction));
                this.rebreakTimer.reset();
                if (slotx != -1) {
                    InvUtils.swapBack();
                }
            }
        }
    }

    public static void stopMine(BlockPos pos) {
        mc.player.networkHandler.sendPacket(new PlayerActionC2SPacket(Action.START_DESTROY_BLOCK, pos.add(0, 300, 0), Direction.UP));
        mc.player.networkHandler.sendPacket(new PlayerActionC2SPacket(Action.STOP_DESTROY_BLOCK, pos.add(0, 300, 0), Direction.UP));
    }

    public void rangeCheck() {
        if (firstBlockDate != null && PlayerUtils.distanceTo(firstBlockDate.pos) > this.range.get().intValue()) {
            firstBlockDate = null;
        }

        if (secondBlockDate != null && PlayerUtils.distanceTo(secondBlockDate.pos) > this.range.get().intValue()) {
            firstBlockDate = null;
        }

        if (this.rebreakBlockDate != null && PlayerUtils.distanceTo(this.rebreakBlockDate.pos) > this.range.get().intValue()) {
            this.rebreakBlockDate = null;
        }
    }

    public void mineBlock(BlockPos pos, Direction direction) {
        mc.player.swingHand(Hand.MAIN_HAND);
        mc.player.networkHandler.sendPacket(new PlayerActionC2SPacket(Action.STOP_DESTROY_BLOCK, pos, direction));
        mc.player.swingHand(Hand.MAIN_HAND);
        mc.player.networkHandler.sendPacket(new PlayerActionC2SPacket(Action.START_DESTROY_BLOCK, pos, direction));
        mc.player.swingHand(Hand.MAIN_HAND);
        if (!this.doubleBreak.get()) {
            mc.player.networkHandler.sendPacket(new PlayerActionC2SPacket(Action.ABORT_DESTROY_BLOCK, pos, direction));
        } else {
            mc.player.networkHandler.sendPacket(new PlayerActionC2SPacket(Action.STOP_DESTROY_BLOCK, pos, direction));
        }
    }

    @EventHandler
    public void onPacket(PacketEvent.Send event) {
        if (event.packet instanceof PlayerActionC2SPacket playerActionC2SPacket) {
            if (playerActionC2SPacket.getAction() == Action.START_DESTROY_BLOCK
                    && godBlocks.contains(mc.world.getBlockState(playerActionC2SPacket.getPos()).getBlock())) {
                event.cancel();
                stopMine(playerActionC2SPacket.getPos());
            }

            if (playerActionC2SPacket.getAction() == Action.ABORT_DESTROY_BLOCK
                    && godBlocks.contains(mc.world.getBlockState(playerActionC2SPacket.getPos()).getBlock())) {
                event.cancel();
                stopMine(playerActionC2SPacket.getPos());
            }

            if (playerActionC2SPacket.getAction() == Action.STOP_DESTROY_BLOCK
                    && godBlocks.contains(mc.world.getBlockState(playerActionC2SPacket.getPos()).getBlock())) {
                event.cancel();
                stopMine(playerActionC2SPacket.getPos());
            }
        }
    }

    @EventHandler
    public void onClickBlock(StartBreakingBlockEvent event) {
        BlockPos pos = event.blockPos;
        Direction direction = event.direction;
        if (!godBlocks.contains(mc.world.getBlockState(pos).getBlock())
                && !this.breakBlocks.contains(pos)
                && !(pos.toCenterPos().distanceTo(mc.player.getPos()) > this.range.get().intValue())) {
            if ((firstBlockDate == null || !pos.equals(firstBlockDate.pos)) && (secondBlockDate == null || !pos.equals(secondBlockDate.pos))) {
                if (firstBlockDate != null && !pos.equals(firstBlockDate.pos) && this.doubleBreak.get()) {
                    if (secondBlockDate == null || !pos.equals(secondBlockDate.pos)) {
                        secondBlockDate = new BlockDate(pos, direction);
                        firstBlockDate.progress = firstBlockDate.progress - (1.0 - this.speed.get()) * MethodAnnotationsScanner$ConstantPool.const_A7O6aSB8Qy8Xrq6;
                    }
                } else if (firstBlockDate == null || !pos.equals(firstBlockDate.pos)) {
                    firstBlockDate = new BlockDate(pos, direction);
                }

                if (this.doubleBreak.get()) {
                    this.mineBlock(pos, direction);
                }

                if (!this.doubleBreak.get()) {
                    event.cancel();
                }
            } else {
                event.cancel();
            }
        }
    }

    @EventHandler
    private void onRender(Render3DEvent event) {
        if (this.render.get()) {
            if (firstBlockDate != null && mc.world.getBlockState(firstBlockDate.pos).getBlock() != Blocks.AIR) {
                BlockPos blockPos = firstBlockDate.pos;
                double progress = firstBlockDate.progress * (1.0 / this.speed.get());
                if (progress > 1.0) {
                    progress = 1.0;
                }

                if (progress < 0.0) {
                    progress = 0.0;
                }

                double x1 = blockPos.getX() + (0.5 - 0.5 * progress);
                double y1 = blockPos.getY()
                        + (0.5 - SocksCommonUtils$ConstantPool.const_Ta0aRbpJzPlleEI * progress);
                double z1 = blockPos.getZ() + (0.5 - 0.5 * progress);
                double x2 = blockPos.getX()
                        + XmlSerializer$ConstantPool.const_a79gj84Lv67ALOi
                        + 0.5 * progress;
                double y2 = blockPos.getY()
                        + 0.5
                        + 0.5 * progress;
                double z2 = blockPos.getZ()
                        + 0.5
                        + 0.5 * progress;
                int side_r = this.sideColor.get().r + (int) ((this.readySideColor.get().r - this.sideColor.get().r) * progress);
                int side_g = this.sideColor.get().g + (int) ((this.readySideColor.get().g - this.sideColor.get().g) * progress);
                int side_b = this.sideColor.get().elementCodec + (int) ((this.readySideColor.get().elementCodec - this.sideColor.get().elementCodec) * progress);
                int side_a = this.sideColor.get().keyCodec + (int) ((this.readySideColor.get().keyCodec - this.sideColor.get().keyCodec) * progress);
                int line_r = this.lineColor.get().r + (int) ((this.readyLineColor.get().r - this.lineColor.get().r) * progress);
                int line_g = this.lineColor.get().g + (int) ((this.readyLineColor.get().g - this.lineColor.get().g) * progress);
                int line_b = this.lineColor.get().elementCodec + (int) ((this.readyLineColor.get().elementCodec - this.lineColor.get().elementCodec) * progress);
                int linr_a = this.lineColor.get().keyCodec + (int) ((this.readyLineColor.get().keyCodec - this.lineColor.get().keyCodec) * progress);
                SettingColor _sideColor = new SettingColor(side_r, side_g, side_b, side_a);
                SettingColor _lineColor = new SettingColor(line_r, line_g, line_b, linr_a);
                event.renderer.box(x1, y1, z1, x2, y2, z2, _sideColor, _lineColor, this.shapeMode.get(), 0);
            }

            if (secondBlockDate != null && mc.world.getBlockState(secondBlockDate.pos).getBlock() != Blocks.AIR) {
                BlockPos blockPosx = secondBlockDate.pos;
                double progressx = secondBlockDate.progress * (1.0 / this.speed.get());
                if (progressx > 1.0) {
                    progressx = 1.0;
                }

                if (progressx < 0.0) {
                    progressx = 0.0;
                }

                double x1 = blockPosx.getX()
                        + (0.5 - Scanners$10$ConstantPool.const_ZiIQ6Evq6pwxAgR * progressx);
                double y1 = blockPosx.getY()
                        + (RegEx$ConstantPool.const_IBmIBOFUyXGmSOK - CtMethod$LongConstParameter$ConstantPool.const_YqnAswLgvFqI2Iv * progressx);
                double z1 = blockPosx.getZ()
                        + (0.5 - 0.5 * progressx);
                double x2 = blockPosx.getX()
                        + ExprEditor$ConstantPool.const_q8NgEat2RIkXB2J
                        + 0.5 * progressx;
                double y2 = blockPosx.getY()
                        + 0.5
                        + Visitor$ConstantPool.const_kXLjsJ6JwOr7J4V * progressx;
                double z2 = blockPosx.getZ() + 0.5 + 0.5 * progressx;
                int side_r = this.sideColor.get().r + (int) ((this.readySideColor.get().r - this.sideColor.get().r) * progressx);
                int side_g = this.sideColor.get().g + (int) ((this.readySideColor.get().g - this.sideColor.get().g) * progressx);
                int side_b = this.sideColor.get().elementCodec + (int) ((this.readySideColor.get().elementCodec - this.sideColor.get().elementCodec) * progressx);
                int side_a = this.sideColor.get().keyCodec + (int) ((this.readySideColor.get().keyCodec - this.sideColor.get().keyCodec) * progressx);
                int line_r = this.lineColor.get().r + (int) ((this.readyLineColor.get().r - this.lineColor.get().r) * progressx);
                int line_g = this.lineColor.get().g + (int) ((this.readyLineColor.get().g - this.lineColor.get().g) * progressx);
                int line_b = this.lineColor.get().elementCodec + (int) ((this.readyLineColor.get().elementCodec - this.lineColor.get().elementCodec) * progressx);
                int linr_a = this.lineColor.get().keyCodec + (int) ((this.readyLineColor.get().keyCodec - this.lineColor.get().keyCodec) * progressx);
                SettingColor _sideColor = new SettingColor(side_r, side_g, side_b, side_a);
                SettingColor _lineColor = new SettingColor(line_r, line_g, line_b, linr_a);
                event.renderer.box(x1, y1, z1, x2, y2, z2, _sideColor, _lineColor, this.shapeMode.get(), 0);
            }

            if (tempBlockDate != null && mc.world.getBlockState(tempBlockDate.pos).getBlock() != Blocks.AIR) {
                BlockPos blockPosxx = tempBlockDate.pos;
                double progressxx = tempBlockDate.progress * (1.0 / this.speed.get());
                if (progressxx > 1.0) {
                    progressxx = 1.0;
                }

                if (progressxx < 0.0) {
                    progressxx = 0.0;
                }

                double x1 = blockPosxx.getX()
                        + (NewExpr$ConstantPool.const_Pbt06YIGNp7Yw4A - 0.5 * progressxx);
                double y1 = blockPosxx.getY()
                        + (Stmnt$ConstantPool.const_DR49Gt2sB9pxAx6 - 0.5 * progressxx);
                double z1 = blockPosxx.getZ()
                        + (0.5 - 0.5 * progressxx);
                double x2 = blockPosxx.getX()
                        + CtClassType$ConstantPool.const_84OtlL6hoigDaGa
                        + 0.5 * progressxx;
                double y2 = blockPosxx.getY()
                        + 0.5
                        + 0.5 * progressxx;
                double z2 = blockPosxx.getZ()
                        + 0.5
                        + 0.5 * progressxx;
                int side_r = this.sideColor.get().r + (int) ((this.readySideColor.get().r - this.sideColor.get().r) * progressxx);
                int side_g = this.sideColor.get().g + (int) ((this.readySideColor.get().g - this.sideColor.get().g) * progressxx);
                int side_b = this.sideColor.get().elementCodec + (int) ((this.readySideColor.get().elementCodec - this.sideColor.get().elementCodec) * progressxx);
                int side_a = this.sideColor.get().keyCodec + (int) ((this.readySideColor.get().keyCodec - this.sideColor.get().keyCodec) * progressxx);
                int line_r = this.lineColor.get().r + (int) ((this.readyLineColor.get().r - this.lineColor.get().r) * progressxx);
                int line_g = this.lineColor.get().g + (int) ((this.readyLineColor.get().g - this.lineColor.get().g) * progressxx);
                int line_b = this.lineColor.get().elementCodec + (int) ((this.readyLineColor.get().elementCodec - this.lineColor.get().elementCodec) * progressxx);
                int linr_a = this.lineColor.get().keyCodec + (int) ((this.readyLineColor.get().keyCodec - this.lineColor.get().keyCodec) * progressxx);
                SettingColor _sideColor = new SettingColor(side_r, side_g, side_b, side_a);
                SettingColor _lineColor = new SettingColor(line_r, line_g, line_b, linr_a);
                event.renderer.box(x1, y1, z1, x2, y2, z2, _sideColor, _lineColor, this.shapeMode.get(), 0);
            }

            if (this.rebreak.get()
                    && this.rebreakBlockDate != null
                    && mc.world.getBlockState(this.rebreakBlockDate.pos).getBlock() != Blocks.AIR
                    && mc.world.getBlockState(this.rebreakBlockDate.pos).getBlock() != Blocks.WATER
                    && mc.world.getBlockState(this.rebreakBlockDate.pos).getBlock() != Blocks.LAVA
                    && firstBlockDate == null
                    && secondBlockDate == null) {
                BlockPos blockPosxxx = this.rebreakBlockDate.pos;
                double x1 = blockPosxxx.getX();
                double y1 = blockPosxxx.getY();
                double z1 = blockPosxxx.getZ();
                double x2 = blockPosxxx.getX() + 1;
                double y2 = blockPosxxx.getY() + 1;
                double z2 = blockPosxxx.getZ() + 1;
                event.renderer.box(x1, y1, z1, x2, y2, z2, this.readySideColor.get(), this.readyLineColor.get(), this.shapeMode.get(), 0);
            }
        }
    }

    public int getBestTool(BlockState blockState) {
        double bestScore = ProxyHandler$LazyChannelPromise$ConstantPool.const_6I2vy7Bm64YG8Rx;
        int bestSlot = -1;

        for (int i = 0; i < 9; i++) {
            double score = mc.player.getInventory().getStack(i).getMiningSpeedMultiplier(blockState);
            if (score > bestScore) {
                bestScore = score;
                bestSlot = i;
            }
        }

        Item item = mc.player.getInventory().getStack(bestSlot).getItem();
        return !(item instanceof PickaxeItem) && !(item instanceof AxeItem) && !(item instanceof ShovelItem) && !(item instanceof SwordItem) ? -1 : bestSlot;
    }

    public BlockDate getBlockDate(BlockPos pos, Direction direction) {
        return new BlockDate(pos, direction);
    }

    public BlockDate getBlockDate(BlockPos pos, Direction direction, boolean rebreak) {
        return new BlockDate(pos, direction, rebreak);
    }

    public class BlockDate {
        public BlockPos pos;
        public Direction direction;
        public boolean done = false;
        public double progress;
        public BlockState blockState;
        public boolean isMining = false;
        public Timer startMiningTimer = new Timer();
        public boolean isBreaked = false;
        public boolean rebreak = true;

        public BlockDate(BlockPos pos, Direction direction) {
            this.pos = pos;
            this.direction = direction;
            this.startMiningTimer.reset();
            this.done = false;
            this.progress = 0.0;
            this.blockState = GrimPacketMine.mc.world.getBlockState(pos);
        }

        public BlockDate(BlockPos pos, Direction direction, boolean rebreak) {
            this.pos = pos;
            this.direction = direction;
            this.startMiningTimer.reset();
            this.done = false;
            this.progress = 0.0;
            this.blockState = GrimPacketMine.mc.world.getBlockState(pos);
            this.rebreak = rebreak;
        }

        public void freshProgress() {
            double bestScore = -1.0;
            int bestSlot = -1;

            for (int i = 0; i < 9; i++) {
                double score = GrimPacketMine.mc.player.getInventory().getStack(i).getMiningSpeedMultiplier(this.blockState);
                if (score > bestScore) {
                    bestScore = score;
                    bestSlot = i;
                }
            }

            Item item = GrimPacketMine.mc.player.getInventory().getStack(bestSlot).getItem();
            if (!(item instanceof PickaxeItem) && !(item instanceof AxeItem) && !(item instanceof ShovelItem) && !(item instanceof SwordItem)) {
                bestSlot = -1;
            }

            if (this.progress <= 1.0 * GrimPacketMine.this.speed.get()) {
                this.progress = this.progress
                        + BlockUtils.getBreakDelta(bestSlot != -1 ? bestSlot : GrimPacketMine.mc.player.getInventory().selectedSlot, this.blockState);
            } else {
                if (bestSlot != -1 && GrimPacketMine.this.swapModeSetting.get() == SwapMode.NORMAL) {
                    InvUtils.swap(bestSlot, false);
                }

                this.done = true;
                this.progress = 1.0;
            }
        }
    }

    public static enum SwapMode {
        SILENT,
        NORMAL;
    }
}
