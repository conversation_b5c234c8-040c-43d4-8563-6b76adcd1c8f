package com.example.addon;

import com.example.addon.modules.*;
import com.mojang.logging.LogUtils;
import meteordevelopment.meteorclient.addons.GithubRepo;
import meteordevelopment.meteorclient.addons.MeteorAddon;
import meteordevelopment.meteorclient.systems.modules.Category;
import meteordevelopment.meteorclient.systems.modules.Modules;
import org.slf4j.Logger;

public class AddonTemplate extends MeteorAddon {
    public static final Logger LOG = LogUtils.getLogger();
    public static final Category MyCategory = new Category("helper");

    @Override
    public void onInitialize() {
        LOG.info("初始化GGB 助手");

        // Modules
        Modules.get().add(new ModuleExample());
        Modules.get().add(new LitematicaMover());
        Modules.get().add(new AutoWalk());
        Modules.get().add(new ShulkerBoxItemFetcher());
        Modules.get().add(new InventoryPrinter());
        Modules.get().add(new TreeAura());
        Modules.get().add(new GGBReader());
        Modules.get().add(new HandsomeSpin());
        Modules.get().add(new PlayerAlert());
        Modules.get().add(new AutoCrystalBlock());
        Modules.get().add(new AutoLog());
        Modules.get().add(new InfiniteElytra());
        Modules.get().add(new RoadBuilder());
        Modules.get().add(new GhostMine());


    }

    @Override
    public void onRegisterCategories() {
        Modules.registerCategory(MyCategory);
    }

    @Override
    public String getPackage() {
        return "com.example.addon";
    }

    @Override
    public GithubRepo getRepo() {
        return new GithubRepo("MeteorDevelopment", "meteor-gg-help");
    }
}
